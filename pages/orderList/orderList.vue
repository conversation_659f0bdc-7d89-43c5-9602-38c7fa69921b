<template>
  <view class="order-list-container" :class="{ 'popup-open': showFilterPopup }">
    <!-- 使用 z-paging 统一管理下拉刷新与上拉加载（H5/APP/小程序跨端稳定） -->
    <z-paging
      ref="paging"
      v-model="filteredOrders"
      :fixed="true"
      :height="'100vh'"
      :auto="true"
      :auto-clean-list-when-reload="true"
      :default-page-size="pageSize"
      :refresher-enabled="!showFilterPopup"
      :refresher-threshold="70"
      :reload-when-refresh="true"
      :show-refresher-when-reload="false"
      :show-loading-more-no-more-view="true"
      :show-default-loading-more-text="true"
      loading-more-no-more-text="我也是有底线的！"
      :class="{ 'z-paging-disabled': showFilterPopup }"
      @refresh="refresh"
      @refresher-status-change="onRefresherStatusChange"
      @query="queryList"
    >
      <!-- 顶部搜索和筛选区域（作为自定义slot头部） -->
      <template #top>
        <view class="search-filter-bar">
          <view class="search-box">
            <view class="search-icon">
              <text class="search-icon-text">🔍</text>
            </view>
            <input
              class="search-input"
              placeholder="搜索固废名称"
              :value="searchKeyword"
              confirm-type="search"
              @input="onSearchInput"
              @confirm="onSearchConfirm"
            />
            <view class="search-action-icons">
              <view class="clear-icon" v-if="searchKeyword" @tap="clearSearch">
                <text class="clear-icon-text">✕</text>
              </view>
              <view class="search-btn" @tap="onSearchConfirm">
                <text class="search-btn-text">搜索</text>
              </view>
            </view>
          </view>
          <view class="filter-btn" @tap="toggleFilterPopup">
            <text class="filter-icon">⚙</text>
            <text class="filter-text">筛选</text>
          </view>
        </view>

        <!-- 状态筛选标签 -->
        <view class="status-filter-container">
          <view class="status-tags">
            <view class="status-tag" :class="{ active: currentStatus === 'all' }" @tap="filterByStatus('all')">全部</view>
            <view class="status-tag" :class="{ active: currentStatus === 'finish' }" @tap="filterByStatus('finish')">审批完成</view>
            <view class="status-tag" :class="{ active: currentStatus === 'contact' }" @tap="filterByStatus('contact')">联系外运</view>
            <view class="status-tag" :class="{ active: currentStatus === 'out-store' }" @tap="filterByStatus('out-store')">已出库</view>
            <view class="status-tag" :class="{ active: currentStatus === 'weight' }" @tap="filterByStatus('weight')">已过磅</view>
            <view class="status-tag" :class="{ active: currentStatus === 'print' }" @tap="filterByStatus('print')">联单已打印</view>
            <view class="status-tag" :class="{ active: currentStatus === 'upload' }" @tap="filterByStatus('upload')">联单已上传</view>
          </view>
        </view>
      </template>

      <!-- 列表内容 -->
      <template #default>
        <view class="order-list">
          <view class="order-item" v-for="item in filteredOrders" :key="item.transfer_id" @tap="goToDetail(item)">
            <view class="order-header">
              <text class="order-id">处置单号：{{ item.apply_code || item.transfer_id }}</text>
              <view class="order-status" :class="item.bpm_status">{{ formatStatus(item.bpm_status) }}</view>
            </view>

            <view class="order-content">
              <view class="order-info">
                <view class="info-item">
                  <text class="info-label">废物类型：</text>
                  <text class="info-value">{{ item.waste_name || '未知' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">申请类型：</text>
                  <text class="info-value">{{ formatApplyType(item.apply_type) }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">处置去向：</text>
                  <text class="info-value">{{ formatDisposalType(item.is_sales) }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">申请人：</text>
                  <text class="info-value">{{ item.user_name || '未知' }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
      <!-- 明确提供无更多数据的slot，确保在NoMore状态时显示提示 -->
      <template #loadingMoreNoMore>
        <view class="no-more">没有更多数据</view>
      </template>

    </z-paging>




    <!-- 悬浮操作按钮：管理员可见 -->
    <view class="fab-button" v-if="userRole === 'admin'" @tap="createNewOrder">
      <view class="fab-icon"></view>
    </view>

    <!-- 筛选弹窗 -->
    <view class="filter-popup" v-if="showFilterPopup" @touchmove.stop.prevent="preventBubble" @tap.stop="preventBubble">
      <view class="filter-popup-mask" @tap="hideFilterPopup" @touchmove.stop.prevent="preventBubble"></view>
      <view class="filter-popup-content" :class="{ 'expanded': showCompanyDropdown }" @tap.stop="preventBubble" @touchmove.stop.prevent="preventBubble">
        <view class="filter-popup-header">
          <text>筛选条件</text>
          <view class="close-icon" @tap.stop="hideFilterPopup">
            <text class="close-icon-text">✕</text>
          </view>
        </view>

        <view class="filter-section" @tap.stop="preventBubble">
          <view class="filter-title">申请类型</view>
          <view class="filter-options">
            <view class="filter-option" :class="{ selected: selectedApplyType === '' }" @tap.stop="selectApplyType('')">全部</view>
            <view
              class="filter-option"
              v-for="option in applyTypeOptions"
              :key="option.value"
              :class="{ selected: selectedApplyType === option.value }"
              @tap.stop="selectApplyType(option.value)"
            >
              {{ option.label }}
            </view>
            <view v-if="applyTypeOptions.length === 0" class="filter-option disabled">
              暂无选项
            </view>
          </view>
        </view>

        <view class="filter-section" @tap.stop="preventBubble">
          <view class="filter-title">处置去向</view>
          <view class="filter-options">
            <view class="filter-option" :class="{ selected: selectedDisposalType === '' }" @tap.stop="selectDisposalType('')">全部</view>
            <view
              class="filter-option"
              v-for="option in disposalTypeOptions"
              :key="option.value"
              :class="{ selected: selectedDisposalType === option.value }"
              @tap.stop="selectDisposalType(option.value)"
            >
              {{ option.label }}
            </view>
            <view v-if="disposalTypeOptions.length === 0" class="filter-option disabled">
              暂无选项
            </view>
          </view>
        </view>

        <view class="filter-section" @tap.stop="preventBubble">
          <view class="filter-title">
            <text>所属公司 ({{ companyList.length }}个)</text>
            <text class="refresh-btn" @tap.stop="refreshCompanyList" v-if="companyList.length === 0">🔄</text>
          </view>
          <view class="company-selector">
            <view class="company-selected" @tap.stop="toggleCompanyDropdown">
              <text class="company-selected-text">{{ selectedCompanyName || '请选择公司' }}</text>
              <text class="dropdown-arrow" :class="{ 'dropdown-arrow-up': showCompanyDropdown }">▼</text>
            </view>
            <view class="company-dropdown" v-if="showCompanyDropdown" @tap.stop="preventBubble">
              <view class="company-search">
                <input
                  class="company-search-input"
                  placeholder="搜索公司名称"
                  :value="companySearchKeyword"
                  @input="onCompanySearchInput"
                  @tap.stop="preventBubble"
                />
              </view>
              <scroll-view class="company-list" scroll-y="true">
                <view class="company-item" @tap.stop="selectCompany('')">
                  <text class="company-item-text" :class="{ selected: selectedCompany === '' }">全部</text>
                </view>
                <view
                  class="company-item"
                  v-for="company in filteredCompanyList"
                  :key="company.org_id"
                  @tap.stop="selectCompany(company.org_id, company.org_name)"
                >
                  <text class="company-item-text" :class="{ selected: selectedCompany === company.org_id }">{{ company.org_name }}</text>
                </view>
                <!-- 调试信息 -->
                <view v-if="filteredCompanyList.length === 0 && companyList.length === 0" class="company-item">
                  <text class="company-item-text" style="color: #999;">正在加载公司列表...</text>
                </view>
                <view v-if="filteredCompanyList.length === 0 && companyList.length > 0" class="company-item">
                  <text class="company-item-text" style="color: #999;">没有找到匹配的公司</text>
                </view>
              </scroll-view>
            </view>
          </view>
        </view>



        <view class="filter-buttons" @tap.stop="preventBubble">
          <button class="reset-btn" @tap.stop="resetFilters">重置</button>
          <button class="apply-btn" @tap.stop="applyFiltersPopup">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrderList, getCompanyList, getFilterOptions, clearSchemaCache } from '@/services/orderService'

export default {
  data() {
    return {
      // 原始订单与筛选后
      orders: [],
      filteredOrders: [],
      // 搜索与筛选
      searchKeyword: '',
      currentStatus: 'all',
      showFilterPopup: false,
      selectedApplyType: '', // 申请类型：equ装置, station固废站
      selectedDisposalType: '', // 处置去向：0直接外委处置, 1有价值处置, 2环保科技处置, 3无价值处置
      selectedCompany: '', // 所属公司ID
      selectedCompanyName: '', // 所属公司名称
      showCompanyDropdown: false, // 公司下拉框显示状态
      companyList: [], // 公司列表
      companySearchKeyword: '', // 公司搜索关键词
      applyTypeOptions: [], // 申请类型选项
      disposalTypeOptions: [], // 处置去向选项
      // 加载状态
      isRefreshing: false,
      isLoadingMore: false,
      noMoreData: false,
      currentPage: 1,
      pageSize: 15, // 与API默认分页大小一致
      // 角色
      userRole: '',
      // 路由类型 hazardous | general
      orderType: 'hazardous',
      // 防抖定时器
      searchTimer: null,
      // 最新请求标识，防止并发时旧响应覆盖新数据
      latestRequestId: '',
      // 上拉加载防抖
      loadMoreTimer: null,
      // 上次触发上拉加载的时间
      lastLoadMoreTime: 0,
      // 加载状态：loadmore | loading | nomore
      loadStatus: 'loadmore',
      // 仅 H5 下 scroll-view 需要的高度（简单估算：窗口高度-头部区高度）
      listHeight: 0
    }
  },

  computed: {
    // 过滤后的公司列表
    filteredCompanyList() {
      if (!this.companySearchKeyword) {
        return this.companyList
      }
      const keyword = this.companySearchKeyword.toLowerCase()
      return this.companyList.filter(company =>
        company.org_name.toLowerCase().includes(keyword)
      )
    }
  },

  onLoad(options) {
    // 仅 H5 计算 scroll-view 高度，确保列表区域可滚且可下拉
    // #ifdef H5
    this.$nextTick(() => {
      this.calcH5ListHeight()
      // 监听窗口尺寸变化时重算
      uni.onWindowResize(() => {
        this.calcH5ListHeight()
      })
    })
    // #endif
    // 读取类型参数：hazardous/general
    if (options && options.type) {
      this.orderType = options.type
    }
    // 根据类型默认预选废物类型，贴近原两个入口期望
    if (this.orderType === 'hazardous') this.selectedWasteTypes = ['危险废物']
    if (this.orderType === 'general') this.selectedWasteTypes = ['一般固废']

    const userInfo = uni.getStorageSync('userInfo') || {}
    this.userRole = userInfo.role || ''

    this.loadOrders()
    this.loadCompanyList()
    this.initFilterOptions()
  },

  onShow() {
    // 页面显示时刷新（但避免与onLoad重复）
    if (this.orders.length > 0) {
      this.$refs.paging?.reload()
    }
  },



  // 使用 z-paging 后，页面级 onPullDownRefresh/onReachBottom 可不再依赖
  // 保留 onPageScroll 作为调试
  onPageScroll(e) {
    console.log('页面滚动: scrollTop=', e?.scrollTop)
  },


  methods: {
    // 加载订单数据（使用真实API）
    async loadOrders() {
      try {
        if (!this.isRefreshing && !this.isLoadingMore) {
          uni.showLoading({ title: '加载中...' })
        }

        // 为本次请求生成唯一ID，避免旧响应覆盖新数据
        const requestId = Math.random().toString(36).slice(2)
        this.latestRequestId = requestId

        // 构造API请求参数
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          status: this.currentStatus,
          keyword: this.searchKeyword,
          startDate: this.startDate,
          endDate: this.endDate
        }

        console.log('请求危废派车列表，参数:', params, 'requestId:', requestId, '当前状态:', {
          currentPage: this.currentPage,
          isRefreshing: this.isRefreshing,
          isLoadingMore: this.isLoadingMore,
          noMoreData: this.noMoreData,
          ordersLength: this.orders.length
        })

        const response = await getOrderList(params)
        console.log('危废派车列表响应:', response, 'requestId:', requestId)

        // 若在等待过程中触发了新请求，直接丢弃旧响应
        if (this.latestRequestId !== requestId) {
          console.warn('丢弃过期响应:', requestId)
          return
        }

        // 处理API响应数据
        const data = response?.data || []
        const pagination = response?.pagination || {}
        const total = response?.total || 0  // 总数据条数

        console.log('API响应结构检查:', {
          responseKeys: Object.keys(response || {}),
          dataLength: data.length,
          pagination: pagination,
          total: total
        })

        this.processOrderData(data, pagination, total)
        this.completeLoading()

      } catch (error) {
        console.error('获取危废派车列表失败:', error)
        uni.showToast({
          title: '获取数据失败',
          icon: 'none',
          duration: 2000
        })

        this.completeLoading()
      }
    },

    processOrderData(data, pagination = {}, total = 0) {
      const list = Array.isArray(data) ? data : []

      console.log('处理分页数据:', {
        currentPage: this.currentPage,
        dataLength: list.length,
        pagination: pagination
      })

      // 首次加载或刷新：直接覆盖
      if (this.currentPage === 1) {
        this.orders = list
      } else {
        // 分页：追加数据
        this.orders = this.orders.concat(list)
      }

      // 渲染源
      this.filteredOrders = this.orders

      // 根据API分页信息判断是否还有更多数据
      if (pagination && typeof pagination.paging !== 'undefined') {
        // 使用API返回的分页信息
        const totalPages = pagination.count || 0  // 总页数
        const currentPage = pagination.current || 1  // 当前页
        const pageSize = pagination.size || this.pageSize  // 每页条数

        // 判断是否还有更多数据：当前页 >= 总页数时，没有更多数据
        this.noMoreData = currentPage >= totalPages
        this.loadStatus = this.noMoreData ? 'nomore' : 'loadmore'

        console.log('分页信息详细:', {
          totalPages: totalPages,
          currentPage: currentPage,
          pageSize: pageSize,
          totalDataCount: total,
          currentPageDataCount: list.length,
          noMoreData: this.noMoreData,
          loadStatus: this.loadStatus,
          calculation: `${currentPage} >= ${totalPages}`
        })
      } else {
        // 备用逻辑：根据返回数据量判断
        this.noMoreData = list.length < this.pageSize
        this.loadStatus = this.noMoreData ? 'nomore' : 'loadmore'
        console.log('使用备用分页逻辑:', {
          dataLength: list.length,
          pageSize: this.pageSize,
          noMoreData: this.noMoreData,
          loadStatus: this.loadStatus
        })
      }
    },

    completeLoading() {
      uni.hideLoading()

      // 重置加载状态
      this.isRefreshing = false
      this.isLoadingMore = false

      console.log('加载完成，状态重置:', {
        isRefreshing: this.isRefreshing,
        isLoadingMore: this.isLoadingMore,
        loadStatus: this.loadStatus,
        noMoreData: this.noMoreData,
        currentPage: this.currentPage,
        totalItems: this.filteredOrders.length
      })
    },

    // z-paging 下拉刷新/重新加载
    // 任何筛选/搜索变更，调用 this.$refs.paging.reload() 即可
    refresh() {
      console.log('z-paging 触发刷新')
      // 重置一些状态
      this.currentPage = 1
      this.noMoreData = false
      this.loadStatus = 'loadmore'
      this.$refs.paging?.reload()
    },

    onRefresherStatusChange(status) {
      // 0默认 1下拉中 2松手立即刷新 3刷新中 4刷新完成
      console.log('refresher status:', status)
    },

    // z-paging 请求回调（入参为当前页码与每页条数）
    async queryList(pageNo, pageSize) {
      try {
        const params = {
          page: pageNo,
          pageSize: pageSize
        }

        // 只有当筛选条件不为空且不为"全部"时才添加到参数中
        if (this.currentStatus && this.currentStatus !== 'all') {
          params.status = this.currentStatus
        }

        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.keyword = this.searchKeyword.trim()
        }

        if (this.selectedApplyType) {
          params.applyType = this.selectedApplyType
        }

        if (this.selectedDisposalType) {
          params.disposalType = this.selectedDisposalType
        }

        if (this.selectedCompany) {
          params.company = this.selectedCompany
        }
        console.log('z-paging queryList 参数:', params)

        const res = await getOrderList(params)
        const list = Array.isArray(res?.data) ? res.data : []

        // 从分页信息计算总条数(totalItems)：totalPages * pageSize
        const pg = res?.pagination
        let total
        if (pg && typeof pg.count === 'number') {
          const size = Number(pg.size || pageSize || this.pageSize || 0)
          total = pg.count * size || undefined
        } else {
          total = res?.total ?? undefined
        }

        // 把原始 orders 也更新（若有其它依赖）
        this.orders = pageNo === 1 ? list : this.orders.concat(list)

        // 通知 z-paging 本次加载完成；传入总条数可避免错误继续加载
        this.$refs.paging.complete(list, total)
      } catch (err) {
        console.error('z-paging 加载失败:', err)
        // 失败也要通知完成，且传空数组以便组件复位
        this.$refs.paging.complete([], 0)
        uni.showToast({ title: '加载失败', icon: 'none' })
      }
    },



    // 上拉加载
    loadMore() {
      // 防止重复触发：检查状态
      if (this.loadStatus !== 'loadmore' || this.isRefreshing) {
        console.log('上拉加载被阻止:', {
          loadStatus: this.loadStatus,
          isRefreshing: this.isRefreshing
        })
        return
      }

      console.log('触发上拉加载，当前页:', this.currentPage, '即将加载页:', this.currentPage + 1)
      this.loadStatus = 'loading'
      this.currentPage += 1
      this.loadOrders()
    },

    // 仅 H5：计算 scroll-view 高度
    // 头部高度动态测量，避免手工估算误差
    calcH5ListHeight() {
      try {
        const q = uni.createSelectorQuery().in(this)
        q.select('.search-filter-bar').boundingClientRect()
        q.select('.status-filter-container').boundingClientRect()
        q.exec((res) => {
          const searchRect = res?.[0] || { height: 0 }
          const statusRect = res?.[1] || { height: 0 }
          const headerPx = (searchRect.height || 0) + (statusRect.height || 0)
          const wh = uni.getSystemInfoSync().windowHeight
          this.listHeight = Math.max(120, wh - headerPx)
          console.log('H5 listHeight计算:', { headerPx, wh, listHeight: this.listHeight })
        })
      } catch (e) {
        const wh = uni.getSystemInfoSync().windowHeight
        this.listHeight = Math.max(120, wh - 84)
      }
    },

    // 搜索
    onSearchInput(e) {
      this.searchKeyword = e.detail.value
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.$refs.paging?.reload()
      }, 400)
    },
    clearSearch() {
      this.searchKeyword = ''
      this.$refs.paging?.reload()
    },

    // 搜索确认
    onSearchConfirm() {
      this.$refs.paging?.reload()
    },

    // 状态筛选
    filterByStatus(status) {
      this.currentStatus = status
      this.$refs.paging?.reload()
    },

    // 筛选弹窗
    toggleFilterPopup() {
      this.showFilterPopup = !this.showFilterPopup
    },
    hideFilterPopup() {
      this.showFilterPopup = false
    },

    // 阻止事件冒泡和穿透
    preventBubble(e) {
      if (e) {
        e.stopPropagation()
        e.preventDefault()
        e.stopImmediatePropagation()
      }
      return false
    },


    // 申请类型筛选
    selectApplyType(type) {
      this.selectedApplyType = type
      this.showCompanyDropdown = false // 收起公司下拉框
    },

    // 处置去向筛选
    selectDisposalType(type) {
      this.selectedDisposalType = type
      this.showCompanyDropdown = false // 收起公司下拉框
    },

    // 初始化筛选选项
    async initFilterOptions() {
      try {
        const options = await getFilterOptions()
        this.applyTypeOptions = options.applyTypeOptions
        this.disposalTypeOptions = options.disposalTypeOptions
        console.log('从接口获取的筛选选项:', options)
      } catch (error) {
        console.error('初始化筛选选项失败:', error)
        this.applyTypeOptions = []
        this.disposalTypeOptions = []
        uni.showToast({ title: '获取筛选选项失败', icon: 'error' })
      }
    },

    // 加载公司列表
    async loadCompanyList() {
      try {
        this.companyList = await getCompanyList()
        console.log('公司列表加载成功:', this.companyList)

        if (!this.companyList || this.companyList.length === 0) {
          console.warn('公司列表为空')
          uni.showToast({ title: '未获取到公司列表', icon: 'none' })
        }
      } catch (error) {
        console.error('加载公司列表失败:', error)
        this.companyList = []
        uni.showToast({ title: '加载公司列表失败', icon: 'error' })
      }
    },

    // 手动刷新公司列表
    async refreshCompanyList() {
      uni.showLoading({ title: '刷新中...' })
      try {
        // 清除缓存的 schema，强制重新获取
        clearSchemaCache()
        await this.loadCompanyList()
        if (this.companyList.length > 0) {
          uni.showToast({ title: '刷新成功', icon: 'success' })
        } else {
          uni.showToast({ title: '仍然无法获取公司列表', icon: 'none' })
        }
      } catch (error) {
        console.error('手动刷新公司列表失败:', error)
        uni.showToast({ title: '刷新失败', icon: 'error' })
      } finally {
        uni.hideLoading()
      }
    },

    // 切换公司下拉框
    toggleCompanyDropdown() {
      this.showCompanyDropdown = !this.showCompanyDropdown
      if (this.showCompanyDropdown) {
        this.companySearchKeyword = ''
        // 展开时，确保弹窗有足够空间
        this.$nextTick(() => {
          console.log('公司下拉框已展开，弹窗已扩大')
        })
      }
    },

    // 公司搜索输入
    onCompanySearchInput(e) {
      this.companySearchKeyword = e.detail.value
    },

    // 选择公司
    selectCompany(orgId, orgName = '') {
      this.selectedCompany = orgId
      this.selectedCompanyName = orgName
      this.showCompanyDropdown = false
      this.companySearchKeyword = ''
      console.log('已选择公司:', orgName, orgId)
    },

    resetFilters() {
      this.selectedApplyType = ''
      this.selectedDisposalType = ''
      this.selectedCompany = ''
      this.selectedCompanyName = ''
      this.showCompanyDropdown = false
      this.companySearchKeyword = ''
    },
    applyFiltersPopup() {
      this.hideFilterPopup()
      this.$refs.paging?.reload()
    },

    // 注释：现在筛选在API层面进行，不需要前端筛选
    // applyFilters() {
    //   // 筛选逻辑已移至API层面
    // },

    // 刷新数据（统一走 z-paging reload）
    refreshData() {
      this.$refs.paging?.reload()
    },





    // 格式化状态显示
    formatStatus(bmpStatus) {
      const statusMap = {
        'finish': '审批完成',
        'contact': '联系外运',
        'out-store': '已出库',
        'weight': '已过磅',
        'print': '联单已打印',
        'upload': '联单已上传',
        'close': '已关闭',
        'cancel': '已取消'
      }
      return statusMap[bmpStatus] || bmpStatus || '未知'
    },

    // 格式化申请类型显示
    formatApplyType(applyType) {
      const typeMap = {
        'equ': '装置',
        'station': '固废站'
      }
      return typeMap[applyType] || applyType || '未知'
    },

    // 格式化处置去向显示
    formatDisposalType(disposalType) {
      const disposalMap = {
        '0': '直接外委处置',
        '1': '有价值处置',
        '2': '环保科技处置',
        '3': '无价值处置'
      }
      return disposalMap[disposalType] || disposalType || '未知'
    },

    // 跳转
    goToDetail(item) {
      // 方案1：使用全局数据传递（简单直接）
      getApp().globalData.currentOrderDetail = item

      const id = item.apply_code || item.transfer_id
      uni.navigateTo({ url: `/pages/orderDetail/orderDetail?id=${id}` })

      // 方案2：也可以使用 uni.$emit 事件传递（备选）
      // uni.$emit('orderDetailData', item)
    },
    createNewOrder() {
      uni.navigateTo({ url: `/pages/transportForm/transportForm` })
    }
  }
}
</script>

<style scoped>
.order-list-container {
  display: block;
  background: #f7f7f7;
}
.order-list-container.popup-open {
  overflow: hidden;
  pointer-events: none;
}
.order-list-container.popup-open .filter-popup {
  pointer-events: auto;
}

/* 弹窗打开时禁用 z-paging 交互 */
.z-paging-disabled {
  pointer-events: none !important;
  touch-action: none !important;
}
.z-paging-disabled * {
  pointer-events: none !important;
}
.search-filter-bar { display: flex; align-items: center; padding: 16rpx 20rpx; background: #ffffff; }
.search-box { display: flex; align-items: center; background: #f5f6f7; border-radius: 999rpx; padding: 0 24rpx; height: 72rpx; flex: 1; }
.search-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}
.search-icon-text {
  font-size: 24rpx;
  color: #c0c4cc;
}
.search-input { flex: 1; font-size: 26rpx; }
.search-action-icons { display: flex; align-items: center; gap: 12rpx; }
.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background: #d9d9d9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.clear-icon-text {
  font-size: 20rpx;
  color: #666;
  line-height: 1;
}
.search-btn {
  padding: 8rpx 16rpx;
  background: #003366;
  border-radius: 36rpx;
  min-width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-btn-text {
  color: #fff;
  font-size: 24rpx;
}
.filter-btn {
  width: 120rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-left: 16rpx;
}
.filter-icon {
  font-size: 28rpx;
  color: #003366;
  margin-bottom: 4rpx;
  line-height: 1;
}
.filter-text {
  font-size: 20rpx;
  color: #003366;
  line-height: 1;
}
.status-filter-container { background: #fff; }
.status-tags {
  display: flex;
  flex-wrap: wrap;
  padding: 16rpx 20rpx;
  gap: 12rpx;
}
.status-tag {
  padding: 12rpx 28rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 999rpx;
  font-size: 24rpx;
  white-space: nowrap;
}
.status-tag.active { background: #003366; color: #fff; }
/* 删除 flex 占满，交给页面自然滚动 */
/* .order-content-area { } */
.empty-state { padding: 160rpx 0; text-align: center; color: #999; }
.order-list { padding: 20rpx; }
.order-item { background: #ffffff; border-radius: 16rpx; padding: 24rpx; margin-bottom: 20rpx; }
.order-header { display: flex; align-items: center; justify-content: space-between; }
.order-id { font-size: 28rpx; color: #333; font-weight: 500; }
.order-status { padding: 8rpx 16rpx; border-radius: 999rpx; font-size: 22rpx; }
/* API状态样式 */
.order-status.finish { background: #fffbe6; color: #faad14; }      /* 审批完成 */
.order-status.contact { background: #e6f7ff; color: #1890ff; }     /* 联系外运 */
.order-status.out-store { background: #f0f9ff; color: #0ea5e9; }   /* 已出库 */
.order-status.weight { background: #fef3c7; color: #d97706; }      /* 已过磅 */
.order-status.print { background: #ecfdf5; color: #059669; }       /* 联单已打印 */
.order-status.upload { background: #f6ffed; color: #52c41a; }      /* 联单已上传 */
.order-status.close { background: #f3f4f6; color: #6b7280; }       /* 已关闭 */
.order-status.cancel { background: #fff1f0; color: #ff4d4f; }      /* 已取消 */
/* 兼容旧状态 */
.order-status.pending { background: #fffbe6; color: #faad14; }
.order-status.processing { background: #e6f7ff; color: #1890ff; }
.order-status.completed { background: #f6ffed; color: #52c41a; }
.order-status.cancelled { background: #fff1f0; color: #ff4d4f; }
.order-content { margin-top: 16rpx; }
.order-info { margin-top: 0; }
.info-item { display: flex; margin-top: 8rpx; }
.info-item:first-child { margin-top: 0; }
.info-label { font-size: 24rpx; color: #666; min-width: 140rpx; }
.info-value { font-size: 26rpx; color: #333; flex: 1; }
.loading-more { display: flex; justify-content: center; padding: 24rpx 0; }
.loading-dot { width: 10rpx; height: 10rpx; background: #d9d9d9; border-radius: 50%; margin: 0 4rpx; }
.no-more { text-align: center; color: #999; padding: 16rpx 0 40rpx; }
.fab-button { position: fixed; right: 30rpx; bottom: 160rpx; width: 100rpx; height: 100rpx; background: #003366; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8rpx 16rpx rgba(0,0,0,0.15); }
.fab-icon { width: 40rpx; height: 40rpx; background: #fff; border-radius: 8rpx; }
/* 筛选弹窗 */
.filter-popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 99999;
  pointer-events: auto;
  touch-action: none;
}
.filter-popup-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0,0,0,0.35);
  pointer-events: auto;
  touch-action: none;
}
.filter-popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  padding: 32rpx;
  pointer-events: auto;
  z-index: 100000;
  touch-action: auto;
  max-height: 60vh;
  min-height: 50vh;
  overflow-y: auto;
  transition: max-height 0.3s ease, min-height 0.3s ease;
}
.filter-popup-content.expanded {
  max-height: 90vh;
  min-height: 75vh;
}
.filter-popup-header { display: flex; justify-content: space-between; align-items: center; font-size: 28rpx; color: #333; }
.close-icon {
  width: 48rpx;
  height: 48rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-icon-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1;
}
.filter-section { margin-top: 32rpx; }
.filter-section:first-child { margin-top: 24rpx; }
.filter-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.refresh-btn {
  font-size: 24rpx;
  color: #003366;
  padding: 8rpx;
  border-radius: 50%;
  background: #f0f0f0;
  min-width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.filter-option {
  padding: 12rpx 24rpx;
  border-radius: 999rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 26rpx;
  white-space: nowrap;
  flex-shrink: 0;
}
.filter-option.selected { background: #003366; color: #fff; }
.filter-option.disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
  pointer-events: none;
}
.company-selector {
  margin-top: 16rpx;
  position: relative;
}
.company-selected {
  height: 72rpx;
  padding: 0 24rpx;
  background: #f5f6f7;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.company-selected-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}
.dropdown-arrow {
  font-size: 20rpx;
  color: #666;
  transition: transform 0.3s;
}
.dropdown-arrow-up {
  transform: rotate(180deg);
}
.company-dropdown {
  position: static;
  margin-top: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
  max-height: none;
}
.company-search {
  padding: 20rpx;
  border-bottom: 1rpx solid #e9ecef;
  background: #fff;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
}
.company-search-input {
  width: 100%;
  height: 60rpx;
  padding: 0 16rpx;
  background: #f5f6f7;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
}
.company-list {
  max-height: 600rpx;
  background: #fff;
  border-bottom-left-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}
.company-item {
  padding: 28rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}
.company-item:hover {
  background-color: #f8f9fa;
}
.company-item:last-child {
  border-bottom: none;
}
.company-item-text {
  font-size: 26rpx;
  color: #333;
}
.company-item-text.selected {
  color: #003366;
  font-weight: 500;
}
.filter-buttons { display: flex; justify-content: space-between; margin-top: 20rpx; }
.reset-btn { flex: 1; margin-right: 12rpx; }
.apply-btn { flex: 1; background: #003366; color: #fff; }
</style>